# 11th September, Thursday, 2025

1. Create /transcribeAndOptimize endpoint in API ✅
2. Edit /optimize endpoint to work without checking usage allowance ✅

# 12th September, Friday, 2025

1. supabaseProvider ✅
2. await in appStartupProvider ✅
3. LoginPage and SignupPage ✅

# 13th September, Saturday, 2025

## Create the HomePage
1. Create the Controls section (language, optimization modes)
    - Optimization mode is gonna be a single String message
    - Append {{ transcript }} when using transcribe-optimize endpoint
    - Optimization modes are gonna be stored in ObjectBox, Inshaa Allah
    - Store Controls model in shared_preferences (store UNIQUE name in shared_prefs)
2. Create shortcuts section (ctrl+shift+8/9/0/-), big and clear with different colors to make it easy to read

## When `supabaseProvider` fails
1. Don't await supabaseProvider in appStartupProvider; await it separately
2. Create a special loading page which awaits supabaseProvider
    - It should have simply our logo and a linear progress indicator.
3. If it fails, show a retry page
    - With a simple fake infinite CircularLoadingIndicator

## Polish auth pages
- Polish the LoginPage and SignupPage
    - Add our logo
    - Center things
    - Create nice elevated buttons
    - Use icons
    - Make the redirect work

# 17th September, Wednesday, 2025

1. Create ControlsRepository and controlsProvider
2. Create recordingService
3. Create MagicService (Transcription, Optimization, Transcription + Optimization)
4. Register global hotkeys and test
