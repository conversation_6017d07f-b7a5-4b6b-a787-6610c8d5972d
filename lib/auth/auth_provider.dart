import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:voicehype/providers/supabase_provider.dart';

part 'auth_provider.g.dart';

@Riverpod(keepAlive: true)
Stream<Session?> authStateChanges(ref) {
  SupabaseClient supabase = ref.read(supabaseProvider).requireValue;
  return supabase.auth.onAuthStateChange.map((event) => event.session);
}

@Riverpod(keepAlive: true)
bool isAuthenticated(ref) {
  SupabaseClient supabase = ref.read(supabaseProvider).requireValue;

  // Watch the stream to update whenever auth state changes
  ref.watch(authStateChangesProvider);

  return supabase.auth.currentSession != null;
}
