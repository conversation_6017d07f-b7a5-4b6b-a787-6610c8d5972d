// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'models/history_item_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(2, 4563428191309913632),
    name: '<PERSON><PERSON><PERSON>',
    lastPropertyId: const obx_int.IdUid(5, 2957209733127025601),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 7577239340471159373),
        name: 'id',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 3263511077094667348),
        name: 'filePath',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 8243657819715821060),
        name: 'dateTime',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 6938497053492835210),
        name: 'transcript',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2957209733127025601),
        name: 'optimizedText',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(4, 3915722442157581664),
    lastIndexId: const obx_int.IdUid(0, 0),
    lastRelationId: const obx_int.IdUid(0, 0),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [
      8548820820716244548,
      2780298869302571417,
      3915722442157581664,
    ],
    retiredIndexUids: const [],
    retiredPropertyUids: const [
      8197162486633852634,
      3906268451426106580,
      8227641102446525188,
      1737680564904100222,
      8694474616754467303,
      5248570039118823834,
      7777618889036435619,
      975729120769524825,
    ],
    retiredRelationUids: const [],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    HistoryItem: obx_int.EntityDefinition<HistoryItem>(
      model: _entities[0],
      toOneRelations: (HistoryItem object) => [],
      toManyRelations: (HistoryItem object) => {},
      getId: (HistoryItem object) => object.id,
      setId: (HistoryItem object, int id) {
        object.id = id;
      },
      objectToFB: (HistoryItem object, fb.Builder fbb) {
        final filePathOffset = fbb.writeString(object.filePath);
        final transcriptOffset = fbb.writeString(object.transcript);
        final optimizedTextOffset = fbb.writeString(object.optimizedText);
        fbb.startTable(6);
        fbb.addInt64(0, object.id);
        fbb.addOffset(1, filePathOffset);
        fbb.addInt64(2, object.dateTime.millisecondsSinceEpoch);
        fbb.addOffset(3, transcriptOffset);
        fbb.addOffset(4, optimizedTextOffset);
        fbb.finish(fbb.endTable());
        return object.id;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final filePathParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final dateTimeParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0),
        );
        final transcriptParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final optimizedTextParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final object = HistoryItem(
          filePath: filePathParam,
          dateTime: dateTimeParam,
          transcript: transcriptParam,
          optimizedText: optimizedTextParam,
        )..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [HistoryItem] entity fields to define ObjectBox queries.
class HistoryItem_ {
  /// See [HistoryItem.id].
  static final id = obx.QueryIntegerProperty<HistoryItem>(
    _entities[0].properties[0],
  );

  /// See [HistoryItem.filePath].
  static final filePath = obx.QueryStringProperty<HistoryItem>(
    _entities[0].properties[1],
  );

  /// See [HistoryItem.dateTime].
  static final dateTime = obx.QueryDateProperty<HistoryItem>(
    _entities[0].properties[2],
  );

  /// See [HistoryItem.transcript].
  static final transcript = obx.QueryStringProperty<HistoryItem>(
    _entities[0].properties[3],
  );

  /// See [HistoryItem.optimizedText].
  static final optimizedText = obx.QueryStringProperty<HistoryItem>(
    _entities[0].properties[4],
  );
}
