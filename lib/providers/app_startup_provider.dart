import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:voicehype/providers/objectbox_provider.dart';
import 'package:voicehype/providers/supabase_provider.dart';
import 'package:voicehype/providers/theme_provider.dart';
import 'package:voicehype/repositories/controls_repository.dart';

part 'app_startup_provider.g.dart';

@riverpod
class AppStartup extends _$AppStartup {
  @override
  Future<bool> build() async {
    // Initialize all required services
    await _initializeServices();
    return true;
  }

  Future<void> _initializeServices() async {
    // Initialize ObjectBox first
    await ref.read(objectBoxProvider.future);

    // Initialize theme
    await ref.read(themeNotifierProvider.future);

    // Initialize controls repository
    await ref.read(controlsRepositoryProvider.future);

    // Initialize Supabase
    await ref.read(supabaseProvider.future);

    // Add other initializations here as needed
  }

  // Method to retry initialization if it fails
  Future<void> retry() async {
    state = const AsyncValue.loading();
    state = await AsyncValue.guard(() async {
      await _initializeServices();
      return true;
    });
  }
}
