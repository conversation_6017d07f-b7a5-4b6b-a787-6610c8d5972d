// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'objectbox_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

@ProviderFor(objectBox)
const objectBoxProvider = ObjectBoxProvider._();

final class ObjectBoxProvider
    extends
        $FunctionalProvider<
          AsyncValue<ObjectBox>,
          ObjectBox,
          FutureOr<ObjectBox>
        >
    with $FutureModifier<ObjectBox>, $FutureProvider<ObjectBox> {
  const ObjectBoxProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'objectBoxProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$objectBoxHash();

  @$internal
  @override
  $FutureProviderElement<ObjectBox> $createElement($ProviderPointer pointer) =>
      $FutureProviderElement(pointer);

  @override
  FutureOr<ObjectBox> create(Ref ref) {
    return objectBox(ref);
  }
}

String _$objectBoxHash() => r'0e9419a55e3ca52e62bf1ff67accf4c47073fae0';

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
