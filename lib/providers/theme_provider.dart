import 'package:flutter/material.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'theme_provider.g.dart';

@Riverpod(keepAlive: true)
class ThemeNotifier extends _$ThemeNotifier {
  static const String _themeKey = 'theme_mode';

  @override
  Future<ThemeMode> build() async {
    // Initialize with system theme as default
    return await initTheme();
  }

  // Initialize theme from shared preferences
  Future<ThemeMode> initTheme() async {
    final prefs = await SharedPreferences.getInstance();
    final savedTheme = prefs.getString(_themeKey);

    // Default to system theme if no saved preference i.e. the first time
    // of app launch
    ThemeMode themeMode = ThemeMode.system;

    if (savedTheme != null) {
      themeMode = ThemeMode.values.firstWhere(
        (mode) => mode.toString() == savedTheme,
        orElse: () => ThemeMode.system,
      );
    }

    return themeMode;
  }

  // Set theme mode and save to shared preferences
  Future<void> setThemeMode(ThemeMode mode) async {
    state = AsyncValue.data(mode);
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_themeKey, mode.toString());
  }
}
