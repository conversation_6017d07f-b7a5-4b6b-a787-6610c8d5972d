import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';

/// Optimization Model Selector Widget
///
/// Provides a dropdown for selecting LLM models for text optimization
/// Supports Claude, Llama, and DeepSeek models
class OptimizationModelSelector extends StatelessWidget {
  final String selectedModel;
  final Function(String) onModelChanged;

  const OptimizationModelSelector({
    super.key,
    required this.selectedModel,
    required this.onModelChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? VoiceHypeColors.darkSurface
            : VoiceHypeColors.lightSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? VoiceHypeColors.darkBorder
              : VoiceHypeColors.lightBorder,
        ),
      ),
      child: Column(
        children: [
          DropdownButtonFormField<String>(
            value: selectedModel,
            decoration: InputDecoration(
              prefixIcon: Icon(
                Icons.psychology,
                color: isDark
                    ? VoiceHypeColors.darkTextSecondary
                    : VoiceHypeColors.lightTextSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            dropdownColor: isDark
                ? VoiceHypeColors.darkSurface
                : VoiceHypeColors.lightSurface,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDark
                  ? VoiceHypeColors.darkTextPrimary
                  : VoiceHypeColors.lightTextPrimary,
            ),
            items: _getModelOptions().map((model) {
              return DropdownMenuItem<String>(
                value: model.id,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      model.name,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                onModelChanged(value);
              }
            },
          ),
        ],
      ),
    );
  }

  List<ModelOption> _getModelOptions() {
    return [
      // Claude Models (Default)
      const ModelOption(
        id: 'claude-4-sonnet',
        name: 'Claude 4 Sonnet',
        category: 'Claude',
      ),
      const ModelOption(
        id: 'claude-haiku',
        name: 'Claude Haiku',
        category: 'Claude',
      ),

      // Llama Models
      const ModelOption(
        id: 'llama-3.1-70b',
        name: 'Llama 3.1 70B',
        category: 'Llama',
      ),
      // DeepSeek Models
      const ModelOption(
        id: 'deepseek-v3',
        name: 'DeepSeek V3',
        category: 'DeepSeek',
      ),
    ];
  }
}

class ModelOption {
  final String id;
  final String name;
  final String category;

  const ModelOption({
    required this.id,
    required this.name,
    required this.category,
  });
}
