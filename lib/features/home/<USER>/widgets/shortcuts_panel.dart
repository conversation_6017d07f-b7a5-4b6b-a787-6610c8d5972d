import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

class ShortcutsPanel extends StatelessWidget {
  const ShortcutsPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final shortcuts = _getShortcuts(context);

    return ListView.separated(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: shortcuts.length,
      separatorBuilder: (context, index) => const SizedBox(height: 30),
      itemBuilder: (context, index) {
        final shortcut = shortcuts[index];
        return Row(
          children: [
            // Icon
            Icon(
              shortcut.icon,
              color: isDark
                  ? VoiceHypeColors.darkTextPrimary
                  : VoiceHypeColors.lightTextPrimary,
            ),
            const SizedBox(width: 16),
            // Shortcut keys (big + bold)
            _buildKeyCombination(context, shortcut.keys, isDark),
            const SizedBox(width: 16),
            // Title text
            Expanded(
              child: Text(
                shortcut.description,
                style: theme.textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: isDark
                      ? VoiceHypeColors.darkTextPrimary
                      : VoiceHypeColors.lightTextPrimary,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildKeyCombination(
    BuildContext context,
    List<String> keys,
    bool isDark,
  ) {
    final List<Widget> keyWidgets = [];

    for (int i = 0; i < keys.length; i++) {
      keyWidgets.add(_buildKeyBox(context, keys[i], isDark));
      if (i < keys.length - 1) {
        keyWidgets.add(
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 2.0),
            child: Text(
              '+',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isDark
                    ? VoiceHypeColors.darkTextSecondary
                    : VoiceHypeColors.lightTextSecondary,
                fontSize: 16,
              ),
            ),
          ),
        );
      }
    }

    return Row(mainAxisSize: MainAxisSize.min, children: keyWidgets);
  }

  Widget _buildKeyBox(BuildContext context, String key, bool isDark) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: isDark ? VoiceHypeColors.darkSurface : const Color(0xFFEDEDED),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isDark
              ? VoiceHypeColors.darkBorder
              : VoiceHypeColors.lightBorder,
        ),
      ),
      child: Text(
        key,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.bold,
          fontFamily: 'Roboto Mono',
          color: isDark
              ? VoiceHypeColors.darkTextPrimary
              : VoiceHypeColors.lightTextPrimary,
        ),
      ),
    );
  }

  List<ShortcutInfo> _getShortcuts(BuildContext context) {
    final bool isMac = !kIsWeb && Platform.isMacOS;
    final String ctrlKey = isMac ? '⌘' : 'Ctrl';

    return [
      ShortcutInfo(
        keys: [ctrlKey, 'Alt', '8'],
        description: 'Start Recording',
        icon: Icons.mic_rounded,
      ),
      ShortcutInfo(
        keys: [ctrlKey, 'Alt', '9'],
        description: 'Start with Optimization',
        icon: Icons.auto_graph_rounded,
      ),
      ShortcutInfo(
        keys: [ctrlKey, 'Alt', '0'],
        description: 'Pause/Resume',
        icon: Icons.pause_circle_outline_rounded,
      ),
      ShortcutInfo(
        keys: [ctrlKey, 'Alt', '-'],
        description: 'Cancel Recording',
        icon: Icons.cancel_outlined,
      ),
      ShortcutInfo(
        keys: [ctrlKey, 'Alt', '.'],
        description: 'Edit Selected Text',
        icon: Icons.edit_note_rounded,
      ),
    ];
  }
}

class ShortcutInfo {
  final List<String> keys;
  final String description;
  final IconData icon;

  const ShortcutInfo({
    required this.keys,
    required this.description,
    required this.icon,
  });
}
