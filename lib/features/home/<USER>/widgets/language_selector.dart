import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';

/// Language Selector Widget
///
/// Provides a dropdown for selecting from the supported languages.
class LanguageSelector extends StatelessWidget {
  final String selectedLanguage;
  final Function(String) onLanguageChanged;

  const LanguageSelector({
    super.key,
    required this.selectedLanguage,
    required this.onLanguageChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDark
            ? VoiceHypeColors.darkSurface
            : VoiceHypeColors.lightSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark
              ? VoiceHypeColors.darkBorder
              : VoiceHypeColors.lightBorder,
        ),
      ),
      child: DropdownButtonFormField<String>(
        value: selectedLanguage,
        isExpanded: true, // Ensures the dropdown text doesn't overflow
        decoration: InputDecoration(
          prefixIcon: Icon(
            Icons.language,
            color: isDark
                ? VoiceHypeColors.darkTextSecondary
                : VoiceHypeColors.lightTextSecondary,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        dropdownColor: isDark
            ? VoiceHypeColors.darkSurface
            : VoiceHypeColors.lightSurface,
        style: theme.textTheme.bodyMedium?.copyWith(
          color: isDark
              ? VoiceHypeColors.darkTextPrimary
              : VoiceHypeColors.lightTextPrimary,
        ),
        items: _getLanguageOptions().map((language) {
          return DropdownMenuItem<String>(
            value: language.code,
            child: Text(
              language.name,
              overflow: TextOverflow
                  .ellipsis, // Prevents long names from breaking layout
            ),
          );
        }).toList(),
        onChanged: (value) {
          if (value != null) {
            onLanguageChanged(value);
          }
        },
      ),
    );
  }

  List<LanguageOption> _getLanguageOptions() {
    return [
      const LanguageOption(code: 'en', name: 'English'),
      const LanguageOption(code: 'af', name: 'Afrikaans'),
      const LanguageOption(code: 'sq', name: 'Albanian'),
      const LanguageOption(code: 'am', name: 'Amharic'),
      const LanguageOption(code: 'ar', name: 'Arabic'),
      const LanguageOption(code: 'hy', name: 'Armenian'),
      const LanguageOption(code: 'as', name: 'Assamese'),
      const LanguageOption(code: 'az', name: 'Azerbaijani'),
      const LanguageOption(code: 'ba', name: 'Bashkir'),
      const LanguageOption(code: 'eu', name: 'Basque'),
      const LanguageOption(code: 'be', name: 'Belarusian'),
      const LanguageOption(code: 'bn', name: 'Bengali'),
      const LanguageOption(code: 'bs', name: 'Bosnian'),
      const LanguageOption(code: 'br', name: 'Breton'),
      const LanguageOption(code: 'bg', name: 'Bulgarian'),
      const LanguageOption(code: 'my', name: 'Burmese'),
      const LanguageOption(code: 'ca', name: 'Catalan'),
      const LanguageOption(code: 'zh', name: 'Chinese'),
      const LanguageOption(code: 'hr', name: 'Croatian'),
      const LanguageOption(code: 'cs', name: 'Czech'),
      const LanguageOption(code: 'da', name: 'Danish'),
      const LanguageOption(code: 'nl', name: 'Dutch'),
      const LanguageOption(code: 'et', name: 'Estonian'),
      const LanguageOption(code: 'fo', name: 'Faroese'),
      const LanguageOption(code: 'fi', name: 'Finnish'),
      const LanguageOption(code: 'fr', name: 'French'),
      const LanguageOption(code: 'gl', name: 'Galician'),
      const LanguageOption(code: 'ka', name: 'Georgian'),
      const LanguageOption(code: 'de', name: 'German'),
      const LanguageOption(code: 'el', name: 'Greek'),
      const LanguageOption(code: 'gu', name: 'Gujarati'),
      const LanguageOption(code: 'ht', name: 'Haitian Creole'),
      const LanguageOption(code: 'ha', name: 'Hausa'),
      const LanguageOption(code: 'haw', name: 'Hawaiian'),
      const LanguageOption(code: 'hi', name: 'Hindi'),
      const LanguageOption(code: 'hu', name: 'Hungarian'),
      const LanguageOption(code: 'is', name: 'Icelandic'),
      const LanguageOption(code: 'id', name: 'Indonesian'),
      const LanguageOption(code: 'it', name: 'Italian'),
      const LanguageOption(code: 'ja', name: 'Japanese'),
      const LanguageOption(code: 'jv', name: 'Javanese'),
      const LanguageOption(code: 'kn', name: 'Kannada'),
      const LanguageOption(code: 'kk', name: 'Kazakh'),
      const LanguageOption(code: 'km', name: 'Khmer'),
      const LanguageOption(code: 'ko', name: 'Korean'),
      const LanguageOption(code: 'lo', name: 'Lao'),
      const LanguageOption(code: 'la', name: 'Latin'),
      const LanguageOption(code: 'lv', name: 'Latvian'),
      const LanguageOption(code: 'ln', name: 'Lingala'),
      const LanguageOption(code: 'lt', name: 'Lithuanian'),
      const LanguageOption(code: 'lb', name: 'Luxembourgish'),
      const LanguageOption(code: 'mk', name: 'Macedonian'),
      const LanguageOption(code: 'mg', name: 'Malagasy'),
      const LanguageOption(code: 'ms', name: 'Malay'),
      const LanguageOption(code: 'ml', name: 'Malayalam'),
      const LanguageOption(code: 'mt', name: 'Maltese'),
      const LanguageOption(code: 'mi', name: 'Maori'),
      const LanguageOption(code: 'mr', name: 'Marathi'),
      const LanguageOption(code: 'mn', name: 'Mongolian'),
      const LanguageOption(code: 'ne', name: 'Nepali'),
      const LanguageOption(code: 'no', name: 'Norwegian'),
      const LanguageOption(code: 'oc', name: 'Occitan'),
      const LanguageOption(code: 'ps', name: 'Pashto'),
      const LanguageOption(code: 'fa', name: 'Persian'),
      const LanguageOption(code: 'pl', name: 'Polish'),
      const LanguageOption(code: 'pt', name: 'Portuguese'),
      const LanguageOption(code: 'pa', name: 'Punjabi'),
      const LanguageOption(code: 'ro', name: 'Romanian'),
      const LanguageOption(code: 'ru', name: 'Russian'),
      const LanguageOption(code: 'sa', name: 'Sanskrit'),
      const LanguageOption(code: 'sr', name: 'Serbian'),
      const LanguageOption(code: 'sn', name: 'Shona'),
      const LanguageOption(code: 'sd', name: 'Sindhi'),
      const LanguageOption(code: 'si', name: 'Sinhala'),
      const LanguageOption(code: 'sk', name: 'Slovak'),
      const LanguageOption(code: 'sl', name: 'Slovenian'),
      const LanguageOption(code: 'so', name: 'Somali'),
      const LanguageOption(code: 'es', name: 'Spanish'),
      const LanguageOption(code: 'su', name: 'Sundanese'),
      const LanguageOption(code: 'sw', name: 'Swahili'),
      const LanguageOption(code: 'sv', name: 'Swedish'),
      const LanguageOption(code: 'tl', name: 'Tagalog'),
      const LanguageOption(code: 'tg', name: 'Tajik'),
      const LanguageOption(code: 'ta', name: 'Tamil'),
      const LanguageOption(code: 'tt', name: 'Tatar'),
      const LanguageOption(code: 'te', name: 'Telugu'),
      const LanguageOption(code: 'th', name: 'Thai'),
      const LanguageOption(code: 'bo', name: 'Tibetan'),
      const LanguageOption(code: 'tr', name: 'Turkish'),
      const LanguageOption(code: 'tk', name: 'Turkmen'),
      const LanguageOption(code: 'uk', name: 'Ukrainian'),
      const LanguageOption(code: 'ur', name: 'Urdu'),
      const LanguageOption(code: 'uz', name: 'Uzbek'),
      const LanguageOption(code: 'vi', name: 'Vietnamese'),
      const LanguageOption(code: 'cy', name: 'Welsh'),
      const LanguageOption(code: 'yi', name: 'Yiddish'),
      const LanguageOption(code: 'yo', name: 'Yoruba'),
    ];
  }
}

class LanguageOption {
  final String code;
  final String name;

  const LanguageOption({required this.code, required this.name});
}
