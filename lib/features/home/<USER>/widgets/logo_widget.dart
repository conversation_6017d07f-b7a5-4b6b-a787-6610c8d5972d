import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';
import 'package:voicehype/app/theme/typography.dart';

/// VoiceHype Logo Widget
/// 
/// Displays the VoiceHype logo with proper theming support.
/// For now, uses text-based logo until SVG assets are added.
class LogoWidget extends StatelessWidget {
  const LogoWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          // Logo Icon (placeholder for now)
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: VoiceHypeColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: VoiceHypeColors.primary.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.mic,
              color: Colors.black,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Logo Text
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'VoiceHype',
                style: VoiceHypeTypography.displayMedium.copyWith(
                  color: isDark 
                      ? VoiceHypeColors.darkTextPrimary 
                      : VoiceHypeColors.lightTextPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Voice to Prompt',
                style: VoiceHypeTypography.bodyMedium.copyWith(
                  color: isDark 
                      ? VoiceHypeColors.darkTextSecondary 
                      : VoiceHypeColors.lightTextSecondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
