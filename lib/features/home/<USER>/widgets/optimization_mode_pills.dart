import 'package:flutter/material.dart';
import 'package:voicehype/app/theme/colors.dart';
import 'package:voicehype/models/optimization_mode_model.dart';

/// Optimization Mode Pills Widget
///
/// Displays optimization modes as cylindrical pill-shaped buttons
/// Includes built-in modes and ability to create custom modes
class OptimizationModePills extends StatelessWidget {
  final String selectedMode;
  final Function(String) onModeChanged;

  const OptimizationModePills({
    super.key,
    required this.selectedMode,
    required this.onModeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Built-in modes
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              ..._getBuiltInModes().map(
                (mode) => _buildModePill(
                  context,
                  mode,
                  isSelected: selectedMode == mode.id,
                  isDark: isDark,
                ),
              ),

              // Add custom mode button
              _buildAddCustomModePill(context, isDark),
            ],
          ),

          const SizedBox(height: 16),

          // Selected mode description
          if (_getSelectedMode() != null)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isDark
                    ? VoiceHypeColors.darkSurface
                    : VoiceHypeColors.lightSurface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isDark
                      ? VoiceHypeColors.darkBorder
                      : VoiceHypeColors.lightBorder,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getSelectedMode()!.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: VoiceHypeColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getSelectedMode()!.description,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: isDark
                          ? VoiceHypeColors.darkTextSecondary
                          : VoiceHypeColors.lightTextSecondary,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildModePill(
    BuildContext context,
    OptimizationMode mode, {
    required bool isSelected,
    required bool isDark,
  }) {
    final theme = Theme.of(context);

    Color textColor = isSelected
        ? Colors.black
        : (isDark
              ? VoiceHypeColors.darkTextPrimary
              : VoiceHypeColors.lightTextPrimary);
    Color iconColor = isSelected ? Colors.black : VoiceHypeColors.primary;

    return GestureDetector(
      onTap: () => onModeChanged(mode.id),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? VoiceHypeColors.primary
              : (isDark
                    ? VoiceHypeColors.darkSurface
                    : VoiceHypeColors.lightSurface),
          borderRadius: BorderRadius.circular(20), // Cylindrical shape
          border: Border.all(
            color: isSelected
                ? VoiceHypeColors.primary
                : (isDark
                      ? VoiceHypeColors.darkBorder
                      : VoiceHypeColors.lightBorder),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: VoiceHypeColors.primary.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(mode.icon, size: 16, color: iconColor),
            const SizedBox(width: 6),
            Text(
              mode.name,
              style: theme.textTheme.labelMedium?.copyWith(
                color: textColor,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddCustomModePill(BuildContext context, bool isDark) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => _showCreateCustomModeDialog(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isDark
              ? VoiceHypeColors.darkSurface
              : VoiceHypeColors.lightSurface,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: VoiceHypeColors.primary,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.add, size: 16, color: VoiceHypeColors.primary),
            const SizedBox(width: 4),
            Text(
              'Custom',
              style: theme.textTheme.labelMedium?.copyWith(
                color: VoiceHypeColors.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showCreateCustomModeDialog(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Custom mode creation coming soon, insha\'Allah!'),
        backgroundColor: VoiceHypeColors.primary,
      ),
    );
  }

  OptimizationMode? _getSelectedMode() {
    return _getBuiltInModes().firstWhere(
      (mode) => mode.id == selectedMode,
      orElse: () => _getBuiltInModes().first,
    );
  }

  List<OptimizationMode> _getBuiltInModes() {
    return [
      const OptimizationMode(
        id: 'auto',
        name: 'Auto',
        description:
            'Automatically detects your command.\nAddress VoiceHype directly. For example, "Hey, VoiceHype, write an email to Ahmad about..."',
        icon: Icons.auto_awesome, // Sparkle icon for Auto
      ),
      const OptimizationMode(
        id: 'polish',
        name: 'Polish',
        description:
            'Rewrite the text to sound formal and professional while preserving the speaker\'s message.',
        icon: Icons.brush,
      ),
      const OptimizationMode(
        id: 'message',
        name: 'Message',
        description:
            'Rewrite as a short, casual message that\'s friendly and easy to understand.',
        icon: Icons.chat_bubble_outline,
      ),
      const OptimizationMode(
        id: 'email',
        name: 'Email',
        description:
            'Format as a professional email with a proper greeting, body, and closing.',
        icon: Icons.email_outlined,
      ),
      const OptimizationMode(
        id: 'notes',
        name: 'Notes',
        description: 'Convert the transcript into a clear notes.',
        icon: Icons.notes,
      ),
      const OptimizationMode(
        id: 'blog',
        name: 'Blog',
        description: 'Write a blog post that\'s engaging and informative.',
        icon: Icons.article,
      ),
    ];
  }
}
