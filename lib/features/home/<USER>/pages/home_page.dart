import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voicehype/features/home/<USER>/widgets/logo_widget.dart';
import 'package:voicehype/features/home/<USER>/widgets/language_selector.dart';
import 'package:voicehype/features/home/<USER>/widgets/optimization_model_selector.dart';
import 'package:voicehype/features/home/<USER>/widgets/optimization_mode_pills.dart';
import 'package:voicehype/features/home/<USER>/widgets/shortcuts_panel.dart';
import 'package:voicehype/app/theme/colors.dart';

/// VoiceHype Home Page
///
/// Bismillahir Rahmanir Raheem
///
/// The main interface for VoiceHype with controls on the left and shortcuts on the right.
/// This page provides access to all core functionality including language selection,
/// model selection, optimization modes, and keyboard shortcuts.
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  String selectedLanguage = 'en';
  String selectedOptimizationModel = 'claude-4-sonnet';
  String selectedOptimizationMode = 'clean-up';

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark
          ? VoiceHypeColors.darkBackground
          : VoiceHypeColors.lightBackground,
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? VoiceHypeColors.darkGradient : null,
        ),
        child: Row(
          children: [
            // Left Side - Controls
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    // Logo Section
                    const LogoWidget(),

                    // Language Selector
                    _buildSectionTitle('Language'),
                    const SizedBox(height: 12),
                    LanguageSelector(
                      selectedLanguage: selectedLanguage,
                      onLanguageChanged: (language) {
                        setState(() {
                          selectedLanguage = language;
                        });
                      },
                    ),

                    const SizedBox(height: 32),

                    // Optimization Model Selector
                    _buildSectionTitle('Optimization Model'),
                    const SizedBox(height: 12),
                    OptimizationModelSelector(
                      selectedModel: selectedOptimizationModel,
                      onModelChanged: (model) {
                        setState(() {
                          selectedOptimizationModel = model;
                        });
                      },
                    ),

                    const SizedBox(height: 32),

                    // Optimization Modes
                    _buildSectionTitle('Optimization Modes'),
                    const SizedBox(height: 12),
                    Expanded(
                      child: OptimizationModePills(
                        selectedMode: selectedOptimizationMode,
                        onModeChanged: (mode) {
                          setState(() {
                            selectedOptimizationMode = mode;
                          });
                        },
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // Vertical Divider
            Container(
              width: 1,
              color: isDark
                  ? VoiceHypeColors.darkBorder
                  : VoiceHypeColors.lightBorder,
            ),

            // Right Side - Shortcuts
            Expanded(
              flex: 3,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('Keyboard Shortcuts'),
                    const SizedBox(height: 24),
                    const Expanded(child: ShortcutsPanel()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.brightness == Brightness.dark
            ? VoiceHypeColors.darkTextPrimary
            : VoiceHypeColors.lightTextPrimary,
      ),
    );
  }
}
