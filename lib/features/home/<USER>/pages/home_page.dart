import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:voicehype/features/home/<USER>/widgets/logo_widget.dart';
import 'package:voicehype/features/home/<USER>/widgets/language_selector.dart';
import 'package:voicehype/features/home/<USER>/widgets/optimization_model_selector.dart';
import 'package:voicehype/features/home/<USER>/widgets/optimization_mode_pills.dart';
import 'package:voicehype/features/home/<USER>/widgets/shortcuts_panel.dart';
import 'package:voicehype/app/theme/colors.dart';
import 'package:voicehype/repositories/controls_repository.dart';

/// VoiceHype Home Page
///
/// Bismillahir Rahmanir Raheem
///
/// The main interface for VoiceHype with controls on the left and shortcuts on the right.
/// This page provides access to all core functionality including language selection,
/// model selection, optimization modes, and keyboard shortcuts.
class HomePage extends ConsumerStatefulWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    // Watch the controls repository
    final controlsAsync = ref.watch(controlsRepositoryProvider);

    return controlsAsync.when(
      loading: () =>
          const Scaffold(body: Center(child: CircularProgressIndicator())),
      error: (error, stack) => Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error, size: 64, color: Colors.red),
              const SizedBox(height: 16),
              Text('Error loading controls: $error'),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () => ref.invalidate(controlsRepositoryProvider),
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      ),
      data: (controls) => _buildHomePage(context, theme, isDark, controls),
    );
  }

  Widget _buildHomePage(
    BuildContext context,
    ThemeData theme,
    bool isDark,
    controls,
  ) {
    return Scaffold(
      backgroundColor: isDark
          ? VoiceHypeColors.darkBackground
          : VoiceHypeColors.lightBackground,
      body: Container(
        decoration: BoxDecoration(
          gradient: isDark ? VoiceHypeColors.darkGradient : null,
        ),
        child: Row(
          children: [
            // Left Side - Controls
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    // Logo Section
                    const LogoWidget(),

                    // Language Selector
                    _buildSectionTitle('Language'),
                    const SizedBox(height: 12),
                    LanguageSelector(
                      selectedLanguage: controls.language,
                      onLanguageChanged: (language) {
                        ref
                            .read(controlsRepositoryProvider.notifier)
                            .saveLanguage(language);
                      },
                    ),

                    const SizedBox(height: 32),

                    // Optimization Model Selector
                    _buildSectionTitle('Optimization Model'),
                    const SizedBox(height: 12),
                    OptimizationModelSelector(
                      selectedModel: controls.optimizationModel,
                      onModelChanged: (model) {
                        ref
                            .read(controlsRepositoryProvider.notifier)
                            .saveOptimizationModel(model);
                      },
                    ),

                    const SizedBox(height: 32),

                    // Optimization Modes
                    _buildSectionTitle('Optimization Modes'),
                    const SizedBox(height: 12),
                    Expanded(
                      child: OptimizationModePills(
                        selectedMode: controls.optimizationModeId,
                        onModeChanged: (mode) {
                          ref
                              .read(controlsRepositoryProvider.notifier)
                              .saveOptimizationMode(mode);
                        },
                      ),
                    ),

                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),

            // Vertical Divider
            Container(
              width: 1,
              color: isDark
                  ? VoiceHypeColors.darkBorder
                  : VoiceHypeColors.lightBorder,
            ),

            // Right Side - Shortcuts
            Expanded(
              flex: 3,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 32),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSectionTitle('Keyboard Shortcuts'),
                    const SizedBox(height: 24),
                    const Expanded(child: ShortcutsPanel()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    final theme = Theme.of(context);
    return Text(
      title,
      style: theme.textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: theme.brightness == Brightness.dark
            ? VoiceHypeColors.darkTextPrimary
            : VoiceHypeColors.lightTextPrimary,
      ),
    );
  }
}
