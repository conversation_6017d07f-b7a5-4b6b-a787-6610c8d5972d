class PlanDetails {
  final String planName;
  final DateTime endDate;
  final int remainingTranscriptionMinutes;
  final int totalTranscriptionMinutes;
  final int remainingOptimizationTokens;
  final int totalOptimizationTokens;

  PlanDetails({
    required this.planName,
    required this.endDate,
    required this.remainingTranscriptionMinutes,
    required this.totalTranscriptionMinutes,
    required this.remainingOptimizationTokens,
    required this.totalOptimizationTokens,
  });
}
