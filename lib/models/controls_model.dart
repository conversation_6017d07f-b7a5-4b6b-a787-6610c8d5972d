class Controls {
  final double sampleRate;
  final String language;
  final String optimizationModel;
  final String optimizationModeId; // Store the ID instead of the full object
  final String inputDevice;

  const Controls({
    required this.sampleRate,
    required this.language,
    required this.optimizationModel,
    required this.optimizationModeId,
    required this.inputDevice,
  });

  // Convert to JSON for SharedPreferences storage
  Map<String, dynamic> toJson() {
    return {
      'sampleRate': sampleRate,
      'language': language,
      'optimizationModel': optimizationModel,
      'optimizationModeId': optimizationModeId,
      'inputDevice': inputDevice,
    };
  }

  // Create from JSON for SharedPreferences retrieval
  factory Controls.fromJson(Map<String, dynamic> json) {
    return Controls(
      sampleRate: (json['sampleRate'] as num?)?.toDouble() ?? 16000.0,
      language: json['language'] as String? ?? 'en',
      optimizationModel:
          json['optimizationModel'] as String? ?? 'claude-4-sonnet',
      optimizationModeId: json['optimizationModeId'] as String? ?? 'auto',
      inputDevice: json['inputDevice'] as String? ?? 'default',
    );
  }

  // Create a copy with updated values
  Controls copyWith({
    double? sampleRate,
    String? language,
    String? optimizationModel,
    String? optimizationModeId,
    String? inputDevice,
  }) {
    return Controls(
      sampleRate: sampleRate ?? this.sampleRate,
      language: language ?? this.language,
      optimizationModel: optimizationModel ?? this.optimizationModel,
      optimizationModeId: optimizationModeId ?? this.optimizationModeId,
      inputDevice: inputDevice ?? this.inputDevice,
    );
  }

  // Default controls with Islamic values
  static const Controls defaultControls = Controls(
    sampleRate: 16000.0,
    language: 'en', // English
    optimizationModel: 'claude-4-sonnet', // Claude 4 Sonnet
    optimizationModeId: 'auto', // Auto mode
    inputDevice: 'default',
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Controls &&
        other.sampleRate == sampleRate &&
        other.language == language &&
        other.optimizationModel == optimizationModel &&
        other.optimizationModeId == optimizationModeId &&
        other.inputDevice == inputDevice;
  }

  @override
  int get hashCode {
    return Object.hash(
      sampleRate,
      language,
      optimizationModel,
      optimizationModeId,
      inputDevice,
    );
  }

  @override
  String toString() {
    return 'Controls(sampleRate: $sampleRate, language: $language, optimizationModel: $optimizationModel, optimizationModeId: $optimizationModeId, inputDevice: $inputDevice)';
  }
}
