import 'dart:io';
import 'dart:ffi';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class PasteService {
  static PasteService? _instance;
  static PasteService get instance => _instance ??= PasteService._();
  PasteService._();

  String? _binaryPath;

  Future<void> initialize() async {
    if (_binaryPath != null) return; // Already initialized

    _binaryPath = await _extractBinary();
  }

  Future<String> _extractBinary() async {
    // Get the correct asset path based on platform
    String assetPath = _getAssetPath();
    String fileName = _getFileName();

    try {
      // Load binary from assets
      final byteData = await rootBundle.load(assetPath);

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final binaryDir = Directory(
        path.join(tempDir.path, 'voicehype_binaries'),
      );
      await binaryDir.create(recursive: true);

      // Write binary to temp location
      final binaryFile = File(path.join(binaryDir.path, fileName));
      await binaryFile.writeAsBytes(byteData.buffer.asUint8List());

      // Make executable on Unix systems
      if (Platform.isLinux || Platform.isMacOS) {
        await Process.run('chmod', ['+x', binaryFile.path]);
      }

      return binaryFile.path;
    } catch (e) {
      throw Exception('Failed to extract paste binary: $e');
    }
  }

  String _getAssetPath() {
    if (Platform.isWindows) {
      // Detect Windows architecture
      final isArm64 = _isWindowsArm64();
      return isArm64
          ? 'assets/binaries/windows-arm64/voicehype-paste.exe'
          : 'assets/binaries/windows-x64/voicehype-paste.exe';
    } else if (Platform.isMacOS) {
      // Detect Apple Silicon vs Intel
      final isArm64 = _isAppleSilicon();
      return isArm64
          ? 'assets/binaries/macos-arm64/voicehype-paste'
          : 'assets/binaries/macos-x64/voicehype-paste';
    } else if (Platform.isLinux) {
      return 'assets/binaries/linux-x64/voicehype-paste';
    }
    throw UnsupportedError(
      'Platform ${Platform.operatingSystem} not supported',
    );
  }

  String _getFileName() {
    if (Platform.isWindows) {
      return 'voicehype-paste.exe';
    } else {
      return 'voicehype-paste';
    }
  }

  bool _isAppleSilicon() {
    // Detect Apple Silicon
    return Platform.version.contains('arm64') ||
        Platform.environment['FLUTTER_TARGET_PLATFORM'] == 'darwin-arm64';
  }

  bool _isWindowsArm64() {
    // Method 1: Check environment variables
    final processor = Platform.environment['PROCESSOR_ARCHITECTURE']
        ?.toLowerCase();
    final processorIdentifier = Platform.environment['PROCESSOR_IDENTIFIER']
        ?.toLowerCase();

    if (processor == 'arm64' || processorIdentifier?.contains('arm') == true) {
      return true;
    }

    // Method 2: Check Dart's architecture info
    if (Abi.current() == Abi.windowsArm64) {
      return true;
    }

    // Method 3: Try to detect from Platform.version (fallback)
    final version = Platform.version.toLowerCase();
    if (version.contains('arm') || version.contains('aarch64')) {
      return true;
    }

    return false; // Default to x64
  }

  /// Get current platform info for debugging
  String getPlatformInfo() {
    final platform = Platform.operatingSystem;
    final version = Platform.version;
    final arch = _getArchitecture();

    return 'Platform: $platform, Architecture: $arch, Version: $version';
  }

  String _getArchitecture() {
    if (Platform.isWindows) {
      return _isWindowsArm64() ? 'ARM64' : 'x64';
    } else if (Platform.isMacOS) {
      return _isAppleSilicon() ? 'Apple Silicon (ARM64)' : 'Intel (x64)';
    } else if (Platform.isLinux) {
      return 'x64';
    }
    return 'Unknown';
  }

  /// Execute paste command
  Future<bool> executePaste() async {
    await initialize();

    try {
      print('🔧 Using binary: $_binaryPath');
      print('📱 ${getPlatformInfo()}');

      final result = await Process.run(_binaryPath!, ['paste']);

      if (result.exitCode == 0) {
        print('✅ Paste executed successfully');
        print('Output: ${result.stdout}');
        return true;
      } else {
        print('❌ Paste failed with exit code: ${result.exitCode}');
        print('Error: ${result.stderr}');
        return false;
      }
    } catch (e) {
      print('❌ Exception executing paste: $e');
      return false;
    }
  }

  /// Paste specific text
  Future<bool> pasteText(String text) async {
    await initialize();

    try {
      print('🔧 Using binary: $_binaryPath');
      print('📱 ${getPlatformInfo()}');

      final result = await Process.run(_binaryPath!, ['paste-text', text]);

      if (result.exitCode == 0) {
        print('✅ Text pasted successfully');
        return true;
      } else {
        print('❌ Failed to paste text: ${result.stderr}');
        return false;
      }
    } catch (e) {
      print('❌ Exception pasting text: $e');
      return false;
    }
  }
}
