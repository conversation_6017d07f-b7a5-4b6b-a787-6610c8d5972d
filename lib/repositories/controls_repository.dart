import 'dart:convert';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:voicehype/models/controls_model.dart';

part 'controls_repository.g.dart';

/// Controls Repository
///
/// Bismillahir Rahmanir Raheem
///
/// Manages user control settings including language, optimization model,
/// optimization mode, sample rate, and input device. All settings are
/// stored in SharedPreferences for persistence across app sessions.
@Riverpod(keepAlive: true)
class ControlsRepository extends _$ControlsRepository {
  static const String _controlsKey = 'voicehype_controls';
  static const String _customOptimizationModesKey = 'custom_optimization_modes';

  @override
  Future<Controls> build() async {
    // Initialize with default controls and load from SharedPreferences
    return await _loadControls();
  }

  /// Load controls from SharedPreferences
  Future<Controls> _loadControls() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final controlsJson = prefs.getString(_controlsKey);

      if (controlsJson != null) {
        final Map<String, dynamic> controlsMap = json.decode(controlsJson);
        return Controls.fromJson(controlsMap);
      }

      // Return default controls if no saved data exists
      return Controls.defaultControls;
    } catch (e) {
      // If there's any error loading, return default controls
      return Controls.defaultControls;
    }
  }

  /// Save controls to SharedPreferences
  Future<void> _saveControls(Controls controls) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final controlsJson = json.encode(controls.toJson());
      await prefs.setString(_controlsKey, controlsJson);

      // Update the state
      state = AsyncValue.data(controls);
    } catch (e) {
      // Handle error gracefully
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// Save language setting
  Future<void> saveLanguage(String language) async {
    final currentControls = state.value ?? Controls.defaultControls;
    final updatedControls = currentControls.copyWith(language: language);
    await _saveControls(updatedControls);
  }

  /// Save optimization model setting
  Future<void> saveOptimizationModel(String model) async {
    final currentControls = state.value ?? Controls.defaultControls;
    final updatedControls = currentControls.copyWith(optimizationModel: model);
    await _saveControls(updatedControls);
  }

  /// Save optimization mode setting
  Future<void> saveOptimizationMode(String modeId) async {
    final currentControls = state.value ?? Controls.defaultControls;
    final updatedControls = currentControls.copyWith(
      optimizationModeId: modeId,
    );
    await _saveControls(updatedControls);
  }

  /// Save sample rate setting
  Future<void> saveSampleRate(double sampleRate) async {
    final currentControls = state.value ?? Controls.defaultControls;
    final updatedControls = currentControls.copyWith(sampleRate: sampleRate);
    await _saveControls(updatedControls);
  }

  /// Save input device setting
  Future<void> saveInputDevice(String inputDevice) async {
    final currentControls = state.value ?? Controls.defaultControls;
    final updatedControls = currentControls.copyWith(inputDevice: inputDevice);
    await _saveControls(updatedControls);
  }

  /// Get current language
  String getCurrentLanguage() {
    return state.value?.language ?? Controls.defaultControls.language;
  }

  /// Get current optimization model
  String getCurrentOptimizationModel() {
    return state.value?.optimizationModel ??
        Controls.defaultControls.optimizationModel;
  }

  /// Get current optimization mode ID
  String getCurrentOptimizationModeId() {
    return state.value?.optimizationModeId ??
        Controls.defaultControls.optimizationModeId;
  }

  /// Get current sample rate
  double getCurrentSampleRate() {
    return state.value?.sampleRate ?? Controls.defaultControls.sampleRate;
  }

  /// Get current input device
  String getCurrentInputDevice() {
    return state.value?.inputDevice ?? Controls.defaultControls.inputDevice;
  }

  /// Reset all controls to default values
  Future<void> resetToDefaults() async {
    await _saveControls(Controls.defaultControls);
  }

  /// Save custom optimization modes to SharedPreferences
  Future<void> saveCustomOptimizationModes(
    List<Map<String, dynamic>> customModes,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customModesJson = json.encode(customModes);
      await prefs.setString(_customOptimizationModesKey, customModesJson);
    } catch (e) {
      // Handle error gracefully - could log this in the future
    }
  }

  /// Load custom optimization modes from SharedPreferences
  Future<List<Map<String, dynamic>>> loadCustomOptimizationModes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customModesJson = prefs.getString(_customOptimizationModesKey);

      if (customModesJson != null) {
        final List<dynamic> customModesList = json.decode(customModesJson);
        return customModesList.cast<Map<String, dynamic>>();
      }

      return [];
    } catch (e) {
      // Return empty list if there's any error
      return [];
    }
  }

  /// Add a new custom optimization mode
  Future<void> addCustomOptimizationMode(
    Map<String, dynamic> customMode,
  ) async {
    final currentModes = await loadCustomOptimizationModes();
    currentModes.add(customMode);
    await saveCustomOptimizationModes(currentModes);
  }

  /// Remove a custom optimization mode by ID
  Future<void> removeCustomOptimizationMode(String modeId) async {
    final currentModes = await loadCustomOptimizationModes();
    currentModes.removeWhere((mode) => mode['id'] == modeId);
    await saveCustomOptimizationModes(currentModes);
  }
}
