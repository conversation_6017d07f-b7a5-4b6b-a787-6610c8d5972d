// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'controls_repository.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

/// Controls Repository
///
/// Bismillahir Rahmanir Raheem
///
/// Manages user control settings including language, optimization model,
/// optimization mode, sample rate, and input device. All settings are
/// stored in SharedPreferences for persistence across app sessions.
@ProviderFor(ControlsRepository)
const controlsRepositoryProvider = ControlsRepositoryProvider._();

/// Controls Repository
///
/// Bismillahir Rahmanir Raheem
///
/// Manages user control settings including language, optimization model,
/// optimization mode, sample rate, and input device. All settings are
/// stored in SharedPreferences for persistence across app sessions.
final class ControlsRepositoryProvider
    extends $AsyncNotifierProvider<ControlsRepository, Controls> {
  /// Controls Repository
  ///
  /// Bismillahir Rahmanir Raheem
  ///
  /// Manages user control settings including language, optimization model,
  /// optimization mode, sample rate, and input device. All settings are
  /// stored in SharedPreferences for persistence across app sessions.
  const ControlsRepositoryProvider._()
    : super(
        from: null,
        argument: null,
        retry: null,
        name: r'controlsRepositoryProvider',
        isAutoDispose: false,
        dependencies: null,
        $allTransitiveDependencies: null,
      );

  @override
  String debugGetCreateSourceHash() => _$controlsRepositoryHash();

  @$internal
  @override
  ControlsRepository create() => ControlsRepository();
}

String _$controlsRepositoryHash() =>
    r'87ec9fa30f127a811fe43548ae634016927b6ba3';

abstract class _$ControlsRepository extends $AsyncNotifier<Controls> {
  FutureOr<Controls> build();
  @$mustCallSuper
  @override
  void runBuild() {
    final created = build();
    final ref = this.ref as $Ref<AsyncValue<Controls>, Controls>;
    final element =
        ref.element
            as $ClassProviderElement<
              AnyNotifier<AsyncValue<Controls>, Controls>,
              AsyncValue<Controls>,
              Object?,
              Object?
            >;
    element.handleValue(ref, created);
  }
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
