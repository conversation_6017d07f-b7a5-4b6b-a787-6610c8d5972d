{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "2:4563428191309913632", "lastPropertyId": "5:2957209733127025601", "name": "HistoryItem", "properties": [{"id": "1:7577239340471159373", "name": "id", "type": 6, "flags": 1}, {"id": "2:3263511077094667348", "name": "filePath", "type": 9}, {"id": "3:8243657819715821060", "name": "dateTime", "type": 10}, {"id": "4:6938497053492835210", "name": "transcript", "type": 9}, {"id": "5:2957209733127025601", "name": "optimizedText", "type": 9}], "relations": []}], "lastEntityId": "4:3915722442157581664", "lastIndexId": "0:0", "lastRelationId": "0:0", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [8548820820716244548, 2780298869302571417, 3915722442157581664], "retiredIndexUids": [], "retiredPropertyUids": [8197162486633852634, 3906268451426106580, 8227641102446525188, 1737680564904100222, 8694474616754467303, 5248570039118823834, 7777618889036435619, 975729120769524825], "retiredRelationUids": [], "version": 1}