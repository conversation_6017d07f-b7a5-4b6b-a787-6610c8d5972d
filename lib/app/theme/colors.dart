import 'package:flutter/material.dart';

/// VoiceHype Color Palette
/// Based on the existing VoiceHype design system from website and extension
class VoiceHypeColors {
  // Primary Colors
  static const Color primary = Color(
    0xFF14F195,
  ); // Bright mint green - signature color
  static const Color primaryHover = Color(0xFF0AD680);
  static const Color secondary = Color(0xFF6366F1); // Indigo
  static const Color secondaryHover = Color(0xFF4F46E5);
  static const Color accent = Color(0xFF0AD6DF); // Cyan
  static const Color accentHover = Color(0xFF08B8C0);

  // Dark Mode Colors (GitHub-style)
  static const Color darkBackground = Color(0xFF0D1117);
  static const Color darkSurface = Color(0xFF161B22);
  static const Color darkSurfaceAlt = Color(0xFF21262D);
  static const Color darkBorder = Color(0xFF30363D);
  static const Color darkTextPrimary = Color(0xFFC9D1D9);
  static const Color darkTextSecondary = Color(0xFF8B949E);

  // Light Mode Colors
  static const Color lightBackground = Color(0xFFFFFFFF);
  static const Color lightSurface = Color(0xFFF6F8FA);
  static const Color lightBorder = Color(0xFFD0D7DE);
  static const Color lightTextPrimary = Color(0xFF24292F);
  static const Color lightTextSecondary = Color(0xFF656D76);

  // Status Colors
  static const Color success = Color(0xFF22C55E);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);

  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, primaryHover],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient darkGradient = LinearGradient(
    colors: [darkBackground, darkSurface],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
}
