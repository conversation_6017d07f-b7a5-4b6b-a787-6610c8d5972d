// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'env.dart';

// **************************************************************************
// EnviedGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// generated_from: .env
final class _Env {
  static const List<int> _enviedkeysupabaseUrl = <int>[
    629403355,
    282209145,
    402821748,
    1747949731,
    632443944,
    2627377231,
    3018257476,
    3171727414,
    2586915026,
    1446012157,
    2736473352,
    2647116468,
    2008870317,
    1723873119,
    732046952,
    31131770,
    3403416107,
    3957409700,
    1167764256,
    1735855440,
    3241390498,
    2573323114,
    3927713449,
    2725022524,
    4127679844,
    242093140,
    1079675312,
    4173919507,
    2780275672,
  ];

  static const List<int> _envieddatasupabaseUrl = <int>[
    629403315,
    282209037,
    402821632,
    1747949779,
    632443995,
    2627377269,
    3018257515,
    3171727385,
    2586914977,
    1446012040,
    2736473464,
    2647116501,
    2008870351,
    1723873086,
    732046875,
    31131679,
    3403416069,
    3957409746,
    1167764303,
    1735855417,
    3241390529,
    2573323023,
    3927713473,
    2725022533,
    4127679764,
    242093105,
    1079675294,
    4173919602,
    2780275633,
  ];

  static final String supabaseUrl = String.fromCharCodes(
    List<int>.generate(
      _envieddatasupabaseUrl.length,
      (int i) => i,
      growable: false,
    ).map((int i) => _envieddatasupabaseUrl[i] ^ _enviedkeysupabaseUrl[i]),
  );

  static const List<int> _enviedkeysupabaseAnonKey = <int>[
    2472723752,
    900095698,
    359688323,
    1648948986,
    3256260701,
    3896326582,
    3297509085,
    2426976444,
    3148273598,
    1900006063,
    1196702434,
    4036539464,
    305793623,
    1181163027,
    1451097322,
    3872343034,
    333354603,
    3478905637,
    2102711465,
    1269990906,
    1468048578,
    720234580,
    2953059101,
    82080337,
    1521931461,
    2536278395,
    676305002,
    3257449333,
    5779929,
    1347251130,
    1294486485,
    2161818253,
    2583894353,
    449542174,
    1131569221,
    2943874686,
    3147098893,
    3877775184,
    698050498,
    3030952647,
    3879637451,
    1175902615,
    4023184342,
    3820607825,
    1784253734,
    2018082043,
    2129632391,
    1400086000,
    275645159,
    3993222705,
    477223348,
    2675043641,
    268237402,
    3513845398,
    2225436290,
    2487339313,
    1251236959,
    1140739418,
    2702435406,
    1732959981,
    1445640964,
    4082261077,
    3500231013,
    2921992108,
    3312503473,
    659859965,
    3898454618,
    2191945755,
    1547367447,
    428640477,
    1367971138,
    3026235789,
    159076367,
    559937314,
    1942878879,
    2694757639,
    2931478307,
    2240202590,
    274716268,
    1965771430,
    998679654,
    126804185,
    229680879,
    3294365129,
    208487965,
    2847424776,
    2071143444,
    964233971,
    3439401329,
    3944685833,
    2673897915,
    1870428196,
    530755569,
    3680882850,
    3172452595,
    3612036766,
    3598386967,
    815797171,
    139872273,
    2115555923,
    1278377088,
    4281785957,
    2308023976,
    4254097658,
    2287228659,
    2664349037,
    2793227543,
    3457295124,
    4183283504,
    1342620389,
    2981281004,
    4263102561,
    257783990,
    4185648476,
    807023594,
    4151613544,
    774089894,
    2186103406,
    3024976724,
    4058758388,
    1438788665,
    3382419982,
    3616420519,
    1367008967,
    3861253742,
    2077897931,
    778499829,
    784551980,
    2848417158,
    933076706,
    88735774,
    2340646976,
    3969193731,
    3738186387,
    2435325929,
    3439554993,
    3022297504,
    2452946833,
    3327426870,
    881433192,
    4002466839,
    518217441,
    4121604549,
    1237692308,
    1673512933,
    2039914839,
    176906275,
    3561004775,
    3716294813,
    3688337551,
    3371594524,
    3215735173,
    459315425,
    315682606,
    2036776387,
    2466434344,
    1108062183,
    2835578028,
    1070418871,
    2110856737,
    2582231090,
    1137098016,
    776840872,
    3562905660,
    3895417448,
    3024874213,
    3973412960,
    666455811,
    1271059987,
  ];

  static const List<int> _envieddatasupabaseAnonKey = <int>[
    2472723789,
    900095659,
    359688393,
    1648948882,
    3256260671,
    3896326641,
    3297509054,
    2426976469,
    3148273649,
    1900006086,
    1196702376,
    4036539393,
    305793538,
    1181163113,
    1451097251,
    3872342987,
    333354533,
    3478905676,
    2102711520,
    1269990793,
    1468048523,
    720234554,
    2953059151,
    82080356,
    1521931430,
    2536278328,
    676304931,
    3257449283,
    5779856,
    1347251153,
    1294486437,
    2161818325,
    2583894279,
    449542237,
    1131569167,
    2943874631,
    3147098915,
    3877775157,
    698050491,
    3030952589,
    3879637426,
    1175902709,
    4023184356,
    3820607785,
    1784253770,
    2018081970,
    2129632493,
    1400085919,
    275645070,
    3993222760,
    477223395,
    2675043596,
    268237356,
    3513845492,
    2225436395,
    2487339384,
    1251236908,
    1140739347,
    2702435363,
    1732959873,
    1445641086,
    4082261046,
    3500230940,
    2921992165,
    3312503431,
    659859892,
    3898454583,
    2191945821,
    1547367550,
    428640441,
    1367971093,
    3026235882,
    159076471,
    559937391,
    1942878965,
    2694757706,
    2931478363,
    2240202519,
    274716165,
    1965771473,
    998679567,
    126804152,
    229680824,
    3294365071,
    208487981,
    2847424833,
    2071143550,
    964233884,
    3439401225,
    3944685895,
    2673897921,
    1870428277,
    530755523,
    3680882927,
    3172452489,
    3612036857,
    3598386982,
    815797246,
    139872379,
    2115555858,
    1278377207,
    4281785897,
    2308024043,
    4254097584,
    2287228575,
    2664348936,
    2793227615,
    3457295189,
    4183283545,
    1342620330,
    2981280902,
    4263102500,
    257783939,
    4185648401,
    807023534,
    4151613497,
    774089950,
    2186103328,
    3024976640,
    4058758321,
    1438788619,
    3382420035,
    3616420579,
    1367008901,
    3861253719,
    2077897957,
    778499760,
    784552007,
    2848417249,
    933076649,
    88735869,
    2340646916,
    3969193826,
    3738186403,
    2435325887,
    3439554945,
    3022297590,
    2452946900,
    3327426907,
    881433144,
    4002466916,
    518217401,
    4121604501,
    1237692409,
    1673512860,
    2039914806,
    176906319,
    3561004756,
    3716294852,
    3688337657,
    3371594596,
    3215735260,
    459315348,
    315682651,
    2036776442,
    2466434425,
    1108062166,
    2835578055,
    1070418831,
    2110856814,
    2582231144,
    1137098106,
    776840926,
    3562905611,
    3895417391,
    3024874134,
    3973412952,
    666455900,
    1271060092,
  ];

  static final String supabaseAnonKey = String.fromCharCodes(
    List<int>.generate(
      _envieddatasupabaseAnonKey.length,
      (int i) => i,
      growable: false,
    ).map(
      (int i) => _envieddatasupabaseAnonKey[i] ^ _enviedkeysupabaseAnonKey[i],
    ),
  );
}
